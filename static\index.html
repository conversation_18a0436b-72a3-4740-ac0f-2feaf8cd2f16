<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>五子棋游戏</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            background-color: #f0f0f0;
            padding: 20px;
        }
        .container {
            max-width: 1400px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
        }
        h1 {
            text-align: center;
            color: #333;
            margin-bottom: 30px;
        }
        .game-layout {
            display: flex;
            gap: 20px;
            justify-content: space-between;
        }
        .left-panel, .right-panel {
            width: 250px;
            flex-shrink: 0;
        }
        .center-panel {
            flex: 1;
            text-align: center;
        }
        .board {
            display: inline-grid;
            grid-template-columns: repeat(15, 25px);
            grid-template-rows: repeat(15, 25px);
            gap: 1px;
            background-color: #8B4513;
            padding: 10px;
            border-radius: 5px;
            margin: 20px 0;
        }
        .cell {
            width: 25px;
            height: 25px;
            background-color: #DEB887;
            border: 1px solid #8B4513;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 16px;
        }
        .cell:hover {
            background-color: #F5DEB3;
        }
        .black { color: black; }
        .white { color: white; text-shadow: 1px 1px 1px black; }
        .controls {
            margin: 20px 0;
            text-align: center;
        }
        button {
            padding: 10px 20px;
            margin: 5px;
            border: none;
            border-radius: 5px;
            background-color: #4CAF50;
            color: white;
            cursor: pointer;
        }
        button:hover {
            background-color: #45a049;
        }
        input {
            padding: 8px;
            margin: 5px;
            border: 1px solid #ddd;
            border-radius: 3px;
        }
        .status {
            margin: 10px 0;
            font-weight: bold;
            font-size: 18px;
        }
        .photo-section {
            border: 1px solid #ddd;
            border-radius: 8px;
            padding: 15px;
            margin-bottom: 20px;
        }
        .photo-container {
            margin-bottom: 20px;
        }
        .photo-container h3 {
            margin: 0 0 10px 0;
            font-size: 14px;
            color: #666;
        }
        .photo {
            width: 200px;
            height: 200px;
            object-fit: cover;
            border-radius: 8px;
            border: 2px solid #ddd;
        }
        .chat-section {
            border: 1px solid #ddd;
            border-radius: 8px;
            padding: 15px;
            height: 500px;
            display: flex;
            flex-direction: column;
        }
        .chat-section h3 {
            margin: 0 0 15px 0;
            font-size: 16px;
            color: #333;
        }
        .chat-messages {
            flex: 1;
            overflow-y: auto;
            border: 1px solid #eee;
            border-radius: 5px;
            padding: 10px;
            margin-bottom: 10px;
            background-color: #fafafa;
        }
        .chat-message {
            margin-bottom: 10px;
            padding: 8px;
            border-radius: 5px;
            background-color: white;
            border-left: 3px solid #4CAF50;
        }
        .chat-message.own {
            border-left-color: #2196F3;
            background-color: #e3f2fd;
        }
        .chat-message .sender {
            font-weight: bold;
            font-size: 12px;
            color: #666;
            margin-bottom: 3px;
        }
        .chat-message .content {
            font-size: 14px;
        }
        .chat-input {
            display: flex;
            gap: 5px;
        }
        .chat-input input {
            flex: 1;
            margin: 0;
        }
        .chat-input button {
            margin: 0;
            padding: 8px 15px;
        }
        #photoUploadArea {
            text-align: center;
            padding: 30px;
            border: 2px dashed #ddd;
            border-radius: 10px;
            margin: 20px 0;
        }
        #photoUploadArea h2 {
            color: #333;
            margin-bottom: 10px;
        }
        #photoUploadArea p {
            color: #666;
            margin-bottom: 20px;
        }
        #uploadStatus {
            margin-top: 10px;
            font-weight: bold;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>情侣五子棋游戏</h1>

        <div id="menu" class="controls">
            <input type="text" id="nicknameInput" placeholder="输入你的昵称" maxlength="20">
            <button onclick="createRoom()">创建房间</button>
            <input type="text" id="roomIdInput" placeholder="输入房间ID">
            <button onclick="joinRoom()">加入房间</button>
        </div>

        <div id="photoUploadArea" style="display: none;">
            <h2>上传你的照片</h2>
            <p>上传一张照片，对方只能看到马赛克版本，直到游戏结束！</p>
            <input type="file" id="photoInput" accept="image/*">
            <button onclick="uploadPhoto()">上传照片</button>
            <div id="uploadStatus"></div>
        </div>

        <div id="gameArea" style="display: none;">
            <div class="game-layout">
                <div class="left-panel">
                    <div class="photo-section">
                        <div class="photo-container">
                            <h3>你的照片</h3>
                            <img id="myPhoto" class="photo" style="display: none;">
                        </div>
                        <div class="photo-container">
                            <h3>对方的照片</h3>
                            <img id="opponentPhoto" class="photo" style="display: none;">
                        </div>
                    </div>
                </div>

                <div class="center-panel">
                    <div class="status" id="status"></div>
                    <div class="board" id="board"></div>
                    <div class="controls">
                        <button onclick="leaveRoom()">离开房间</button>
                    </div>
                </div>

                <div class="right-panel">
                    <div class="chat-section">
                        <h3>聊天</h3>
                        <div id="chatMessages" class="chat-messages"></div>
                        <div class="chat-input">
                            <input type="text" id="messageInput" placeholder="输入消息..." maxlength="200">
                            <button onclick="sendMessage()">发送</button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="game.js"></script>
</body>
</html>