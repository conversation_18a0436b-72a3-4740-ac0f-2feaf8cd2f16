<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>五子棋游戏</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
            margin: 0;
            background-color: #f0f0f0;
        }
        .container {
            text-align: center;
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
        }
        .board {
            display: inline-grid;
            grid-template-columns: repeat(15, 30px);
            grid-template-rows: repeat(15, 30px);
            gap: 1px;
            background-color: #8B4513;
            padding: 10px;
            border-radius: 5px;
        }
        .cell {
            width: 30px;
            height: 30px;
            background-color: #DEB887;
            border: 1px solid #8B4513;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 20px;
        }
        .cell:hover {
            background-color: #F5DEB3;
        }
        .black { color: black; }
        .white { color: white; text-shadow: 1px 1px 1px black; }
        .controls {
            margin: 20px 0;
        }
        button {
            padding: 10px 20px;
            margin: 5px;
            border: none;
            border-radius: 5px;
            background-color: #4CAF50;
            color: white;
            cursor: pointer;
        }
        button:hover {
            background-color: #45a049;
        }
        input {
            padding: 8px;
            margin: 5px;
            border: 1px solid #ddd;
            border-radius: 3px;
        }
        .status {
            margin: 10px 0;
            font-weight: bold;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>五子棋游戏</h1>
        
        <div id="menu" class="controls">
            <button onclick="createRoom()">创建房间</button>
            <input type="text" id="roomIdInput" placeholder="输入房间ID">
            <button onclick="joinRoom()">加入房间</button>
        </div>
        
        <div id="gameArea" style="display: none;">
            <div class="status" id="status"></div>
            <div class="board" id="board"></div>
            <div class="controls">
                <button onclick="leaveRoom()">离开房间</button>
            </div>
        </div>
    </div>

    <script src="game.js"></script>
</body>
</html>