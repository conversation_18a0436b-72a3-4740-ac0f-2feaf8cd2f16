let currentRoomId = null;
let playerId = null;
let playerNumber = null;
let gameState = null;
let socket = null;
let nickname = null;

function createRoom() {
    fetch('/api/create-room')
        .then(response => response.json())
        .then(data => {
            currentRoomId = data.roomId;
            document.getElementById('roomIdInput').value = currentRoomId;
            joinRoom();
        });
}

function joinRoom() {
    const roomId = document.getElementById('roomIdInput').value;
    const nicknameInput = document.getElementById('nicknameInput').value;

    if (!roomId) {
        alert('请输入房间ID');
        return;
    }

    if (!nicknameInput) {
        alert('请输入昵称');
        return;
    }

    nickname = nicknameInput;
    currentRoomId = roomId;

    // 建立WebSocket连接
    const protocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:';
    const wsUrl = `${protocol}//${window.location.host}/ws/${roomId}`;

    socket = new WebSocket(wsUrl);

    socket.onopen = () => {
        console.log('WebSocket连接已建立');
    };

    socket.onmessage = (event) => {
        const data = JSON.parse(event.data);
        console.log('收到消息:', data);

        if (data.type === 'joined') {
            playerId = data.playerId;
            playerNumber = data.playerNumber;
            gameState = data.gameState;

            // 发送昵称
            socket.send(JSON.stringify({
                type: 'setNickname',
                playerId: playerId,
                nickname: nickname
            }));

            document.getElementById('menu').style.display = 'none';
            showPhotoUpload();

        } else if (data.type === 'gameUpdate' || data.type === 'playerJoined') {
            gameState = data.gameState;
            updateDisplay();
        } else if (data.type === 'photoUploaded') {
            gameState = data.gameState;
            checkGamePhase();
        } else if (data.type === 'chatMessage') {
            addChatMessage(data.message);
        } else if (data.type === 'error') {
            alert(data.message);
        }
    };

    socket.onclose = (event) => {
        if (event.code === 1000 && event.reason === '房间已满') {
            alert('房间已满');
        } else {
            console.log('WebSocket连接已关闭');
        }
    };

    socket.onerror = (error) => {
        console.error('WebSocket错误:', error);
        alert('连接失败，请检查房间ID是否正确');
    };
}

function showPhotoUpload() {
    document.getElementById('photoUploadArea').style.display = 'block';
}

function uploadPhoto() {
    const fileInput = document.getElementById('photoInput');
    const file = fileInput.files[0];

    if (!file) {
        alert('请选择一张照片');
        return;
    }

    if (!file.type.startsWith('image/')) {
        alert('请选择图片文件');
        return;
    }

    const formData = new FormData();
    formData.append('photo', file);
    formData.append('playerId', playerId);

    document.getElementById('uploadStatus').textContent = '上传中...';

    fetch(`/api/upload-photo/${currentRoomId}`, {
        method: 'POST',
        body: formData
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            document.getElementById('uploadStatus').textContent = '上传成功！等待对方上传...';
            document.getElementById('photoUploadArea').style.display = 'none';
        } else {
            document.getElementById('uploadStatus').textContent = '上传失败: ' + (data.error || '未知错误');
        }
    })
    .catch(error => {
        console.error('上传错误:', error);
        document.getElementById('uploadStatus').textContent = '上传失败';
    });
}

function checkGamePhase() {
    if (gameState.gamePhase === 'playing') {
        document.getElementById('gameArea').style.display = 'block';
        initBoard();
        updateDisplay();
        updatePhotos();
    }
}

function updatePhotos() {
    // 显示自己的原图
    if (gameState.photos[playerId]) {
        const myPhoto = document.getElementById('myPhoto');
        myPhoto.src = gameState.photos[playerId].originalUrl;
        myPhoto.style.display = 'block';
    }

    // 显示对方的照片（马赛克或原图，取决于游戏状态）
    const opponentId = gameState.players.find(id => id !== playerId);
    if (opponentId && gameState.photos[opponentId]) {
        const opponentPhoto = document.getElementById('opponentPhoto');

        if (gameState.gameOver && gameState.winner === playerNumber) {
            // 胜利者可以看到对方的原图
            opponentPhoto.src = gameState.photos[opponentId].originalUrl;
        } else {
            // 其他情况显示马赛克图
            opponentPhoto.src = gameState.photos[opponentId].mosaicUrl;
        }
        opponentPhoto.style.display = 'block';
    }
}

function sendMessage() {
    const messageInput = document.getElementById('messageInput');
    const message = messageInput.value.trim();

    if (!message) return;

    if (socket && socket.readyState === WebSocket.OPEN) {
        socket.send(JSON.stringify({
            type: 'chat',
            playerId: playerId,
            message: message
        }));

        messageInput.value = '';
    }
}

function addChatMessage(chatMessage) {
    const chatMessages = document.getElementById('chatMessages');
    const messageDiv = document.createElement('div');
    messageDiv.className = 'chat-message' + (chatMessage.playerId === playerId ? ' own' : '');

    const senderDiv = document.createElement('div');
    senderDiv.className = 'sender';
    senderDiv.textContent = chatMessage.playerId === playerId ? '我' : `玩家${chatMessage.playerNumber}`;

    const contentDiv = document.createElement('div');
    contentDiv.className = 'content';
    contentDiv.textContent = chatMessage.message;

    messageDiv.appendChild(senderDiv);
    messageDiv.appendChild(contentDiv);
    chatMessages.appendChild(messageDiv);

    // 滚动到底部
    chatMessages.scrollTop = chatMessages.scrollHeight;
}

// 添加回车发送消息功能
document.addEventListener('DOMContentLoaded', function() {
    const messageInput = document.getElementById('messageInput');
    if (messageInput) {
        messageInput.addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                sendMessage();
            }
        });
    }
});

function leaveRoom() {
    if (socket) {
        socket.close();
        socket = null;
    }

    currentRoomId = null;
    playerId = null;
    playerNumber = null;
    gameState = null;
    nickname = null;

    document.getElementById('menu').style.display = 'block';
    document.getElementById('gameArea').style.display = 'none';
    document.getElementById('photoUploadArea').style.display = 'none';
    document.getElementById('roomIdInput').value = '';
    document.getElementById('nicknameInput').value = '';
}

function initBoard() {
    const board = document.getElementById('board');
    board.innerHTML = '';
    
    for (let row = 0; row < 15; row++) {
        for (let col = 0; col < 15; col++) {
            const cell = document.createElement('div');
            cell.className = 'cell';
            cell.onclick = () => makeMove(row, col);
            cell.dataset.row = row;
            cell.dataset.col = col;
            board.appendChild(cell);
        }
    }
}

function makeMove(row, col) {
    if (!gameState || gameState.gameOver) return;
    if (gameState.currentPlayer !== playerNumber) return;
    if (gameState.board[row][col] !== 0) return;
    if (!socket || socket.readyState !== WebSocket.OPEN) return;

    socket.send(JSON.stringify({
        type: 'move',
        row,
        col,
        playerId
    }));
}

function updateDisplay() {
    if (!gameState) return;

    // 更新棋盘
    const cells = document.querySelectorAll('.cell');
    cells.forEach(cell => {
        const row = parseInt(cell.dataset.row);
        const col = parseInt(cell.dataset.col);
        const value = gameState.board[row][col];

        if (value === 1) {
            cell.textContent = '●';
            cell.className = 'cell black';
        } else if (value === 2) {
            cell.textContent = '●';
            cell.className = 'cell white';
        } else {
            cell.textContent = '';
            cell.className = 'cell';
        }
    });

    // 更新状态
    const status = document.getElementById('status');
    if (gameState.gamePhase === 'waiting') {
        status.textContent = '等待另一位玩家加入...';
        status.style.color = 'orange';
    } else if (gameState.gamePhase === 'uploading') {
        status.textContent = '等待双方上传照片...';
        status.style.color = 'orange';
    } else if (gameState.gameOver) {
        if (gameState.winner === playerNumber) {
            status.textContent = '你赢了！可以查看对方的照片了！';
            status.style.color = 'green';
        } else {
            status.textContent = '你输了！对方可以看到你的照片';
            status.style.color = 'red';
        }
        updatePhotos(); // 游戏结束时更新照片显示
    } else if (gameState.players.length < 2) {
        status.textContent = '等待另一位玩家加入...';
        status.style.color = 'orange';
    } else if (gameState.currentPlayer === playerNumber) {
        status.textContent = '轮到你下棋';
        status.style.color = 'blue';
    } else {
        status.textContent = '等待对手下棋...';
        status.style.color = 'gray';
    }

    // 更新照片显示
    updatePhotos();

    // 更新聊天消息
    updateChatMessages();
}

function updateChatMessages() {
    const chatMessagesContainer = document.getElementById('chatMessages');
    if (!chatMessagesContainer) return;

    // 清空现有消息
    chatMessagesContainer.innerHTML = '';

    // 添加所有聊天消息
    gameState.chatMessages.forEach(message => {
        addChatMessage(message);
    });
}