let currentRoomId = null;
let playerId = null;
let playerNumber = null;
let gameState = null;
let channel = null;

function createRoom() {
    fetch('/api/create-room')
        .then(response => response.json())
        .then(data => {
            currentRoomId = data.roomId;
            document.getElementById('roomIdInput').value = currentRoomId;
            joinRoom();
        });
}

function joinRoom() {
    const roomId = document.getElementById('roomIdInput').value;
    if (!roomId) {
        alert('请输入房间ID');
        return;
    }
    
    fetch(`/api/join-room/${roomId}`)
        .then(response => response.json())
        .then(data => {
            if (data.error) {
                alert(data.error);
                return;
            }
            
            currentRoomId = roomId;
            playerId = data.playerId;
            playerNumber = data.playerNumber;
            gameState = data.gameState;
            
            // 设置广播频道
            channel = new BroadcastChannel(`room_${roomId}`);
            channel.onmessage = (event) => {
                if (event.data.type === 'gameUpdate') {
                    gameState = event.data.gameState;
                    updateDisplay();
                }
            };
            
            document.getElementById('menu').style.display = 'none';
            document.getElementById('gameArea').style.display = 'block';
            
            initBoard();
            updateDisplay();
        })
        .catch(error => {
            alert('加入房间失败: ' + error.message);
        });
}

function leaveRoom() {
    if (channel) {
        channel.close();
    }
    
    currentRoomId = null;
    playerId = null;
    playerNumber = null;
    gameState = null;
    
    document.getElementById('menu').style.display = 'block';
    document.getElementById('gameArea').style.display = 'none';
    document.getElementById('roomIdInput').value = '';
}

function initBoard() {
    const board = document.getElementById('board');
    board.innerHTML = '';
    
    for (let row = 0; row < 15; row++) {
        for (let col = 0; col < 15; col++) {
            const cell = document.createElement('div');
            cell.className = 'cell';
            cell.onclick = () => makeMove(row, col);
            cell.dataset.row = row;
            cell.dataset.col = col;
            board.appendChild(cell);
        }
    }
}

function makeMove(row, col) {
    if (!gameState || gameState.gameOver) return;
    if (gameState.currentPlayer !== playerNumber) return;
    if (gameState.board[row][col] !== 0) return;
    
    fetch(`/api/move/${currentRoomId}`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({ row, col, playerId })
    })
    .then(response => response.json())
    .then(data => {
        if (data.error) {
            alert(data.error);
        }
    });
}

function updateDisplay() {
    if (!gameState) return;
    
    // 更新棋盘
    const cells = document.querySelectorAll('.cell');
    cells.forEach(cell => {
        const row = parseInt(cell.dataset.row);
        const col = parseInt(cell.dataset.col);
        const value = gameState.board[row][col];
        
        if (value === 1) {
            cell.textContent = '●';
            cell.className = 'cell black';
        } else if (value === 2) {
            cell.textContent = '●';
            cell.className = 'cell white';
        } else {
            cell.textContent = '';
            cell.className = 'cell';
        }
    });
    
    // 更新状态
    const status = document.getElementById('status');
    if (gameState.gameOver) {
        if (gameState.winner === playerNumber) {
            status.textContent = '你赢了！';
            status.style.color = 'green';
        } else {
            status.textContent = '你输了！';
            status.style.color = 'red';
        }
    } else if (gameState.players.length < 2) {
        status.textContent = '等待另一位玩家加入...';
        status.style.color = 'orange';
    } else if (gameState.currentPlayer === playerNumber) {
        status.textContent = '轮到你下棋';
        status.style.color = 'blue';
    } else {
        status.textContent = '等待对手下棋...';
        status.style.color = 'gray';
    }
}