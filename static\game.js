let currentRoomId = null;
let playerId = null;
let playerNumber = null;
let gameState = null;
let socket = null;

function createRoom() {
    fetch('/api/create-room')
        .then(response => response.json())
        .then(data => {
            currentRoomId = data.roomId;
            document.getElementById('roomIdInput').value = currentRoomId;
            joinRoom();
        });
}

function joinRoom() {
    const roomId = document.getElementById('roomIdInput').value;
    if (!roomId) {
        alert('请输入房间ID');
        return;
    }

    currentRoomId = roomId;

    // 建立WebSocket连接
    const protocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:';
    const wsUrl = `${protocol}//${window.location.host}/ws/${roomId}`;

    socket = new WebSocket(wsUrl);

    socket.onopen = () => {
        console.log('WebSocket连接已建立');
    };

    socket.onmessage = (event) => {
        const data = JSON.parse(event.data);
        console.log('收到消息:', data);

        if (data.type === 'joined') {
            playerId = data.playerId;
            playerNumber = data.playerNumber;
            gameState = data.gameState;

            document.getElementById('menu').style.display = 'none';
            document.getElementById('gameArea').style.display = 'block';

            initBoard();
            updateDisplay();
        } else if (data.type === 'gameUpdate' || data.type === 'playerJoined') {
            gameState = data.gameState;
            updateDisplay();
        } else if (data.type === 'error') {
            alert(data.message);
        }
    };

    socket.onclose = (event) => {
        if (event.code === 1000 && event.reason === '房间已满') {
            alert('房间已满');
        } else {
            console.log('WebSocket连接已关闭');
        }
    };

    socket.onerror = (error) => {
        console.error('WebSocket错误:', error);
        alert('连接失败，请检查房间ID是否正确');
    };
}

function leaveRoom() {
    if (socket) {
        socket.close();
        socket = null;
    }

    currentRoomId = null;
    playerId = null;
    playerNumber = null;
    gameState = null;

    document.getElementById('menu').style.display = 'block';
    document.getElementById('gameArea').style.display = 'none';
    document.getElementById('roomIdInput').value = '';
}

function initBoard() {
    const board = document.getElementById('board');
    board.innerHTML = '';
    
    for (let row = 0; row < 15; row++) {
        for (let col = 0; col < 15; col++) {
            const cell = document.createElement('div');
            cell.className = 'cell';
            cell.onclick = () => makeMove(row, col);
            cell.dataset.row = row;
            cell.dataset.col = col;
            board.appendChild(cell);
        }
    }
}

function makeMove(row, col) {
    if (!gameState || gameState.gameOver) return;
    if (gameState.currentPlayer !== playerNumber) return;
    if (gameState.board[row][col] !== 0) return;
    if (!socket || socket.readyState !== WebSocket.OPEN) return;

    socket.send(JSON.stringify({
        type: 'move',
        row,
        col,
        playerId
    }));
}

function updateDisplay() {
    if (!gameState) return;
    
    // 更新棋盘
    const cells = document.querySelectorAll('.cell');
    cells.forEach(cell => {
        const row = parseInt(cell.dataset.row);
        const col = parseInt(cell.dataset.col);
        const value = gameState.board[row][col];
        
        if (value === 1) {
            cell.textContent = '●';
            cell.className = 'cell black';
        } else if (value === 2) {
            cell.textContent = '●';
            cell.className = 'cell white';
        } else {
            cell.textContent = '';
            cell.className = 'cell';
        }
    });
    
    // 更新状态
    const status = document.getElementById('status');
    if (gameState.gameOver) {
        if (gameState.winner === playerNumber) {
            status.textContent = '你赢了！';
            status.style.color = 'green';
        } else {
            status.textContent = '你输了！';
            status.style.color = 'red';
        }
    } else if (gameState.players.length < 2) {
        status.textContent = '等待另一位玩家加入...';
        status.style.color = 'orange';
    } else if (gameState.currentPlayer === playerNumber) {
        status.textContent = '轮到你下棋';
        status.style.color = 'blue';
    } else {
        status.textContent = '等待对手下棋...';
        status.style.color = 'gray';
    }
}