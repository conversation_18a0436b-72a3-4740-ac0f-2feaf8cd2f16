import { Hono } from "https://deno.land/x/hono@v3.12.11/mod.ts";
import { serveStatic } from "https://deno.land/x/hono@v3.12.11/middleware.ts";

const app = new Hono();

// 游戏状态管理
interface GameState {
  board: number[][];
  currentPlayer: number;
  players: string[];
  gameOver: boolean;
  winner: number | null;
}

interface Player {
  id: string;
  socket: WebSocket;
}

interface Room {
  id: string;
  gameState: GameState;
  players: Player[];
}

const rooms = new Map<string, Room>();

// 创建新游戏状态
function createGameState(): GameState {
  return {
    board: Array(15).fill(null).map(() => Array(15).fill(0)),
    currentPlayer: 1,
    players: [],
    gameOver: false,
    winner: null
  };
}

// 检查胜利条件
function checkWin(board: number[][], row: number, col: number, player: number): boolean {
  const directions = [[1,0], [0,1], [1,1], [1,-1]];
  
  for (const [dx, dy] of directions) {
    let count = 1;
    
    // 正方向
    for (let i = 1; i < 5; i++) {
      const newRow = row + dx * i;
      const newCol = col + dy * i;
      if (newRow >= 0 && newRow < 15 && newCol >= 0 && newCol < 15 && 
          board[newRow][newCol] === player) {
        count++;
      } else break;
    }
    
    // 反方向
    for (let i = 1; i < 5; i++) {
      const newRow = row - dx * i;
      const newCol = col - dy * i;
      if (newRow >= 0 && newRow < 15 && newCol >= 0 && newCol < 15 && 
          board[newRow][newCol] === player) {
        count++;
      } else break;
    }
    
    if (count >= 5) return true;
  }
  return false;
}

// 广播消息到房间内所有玩家
function broadcastToRoom(room: Room, message: any) {
  room.players.forEach(player => {
    if (player.socket.readyState === WebSocket.OPEN) {
      player.socket.send(JSON.stringify(message));
    }
  });
}

// WebSocket 处理
app.get("/ws/:roomId", (c) => {
  const roomId = c.req.param("roomId");
  
  if (c.req.header("upgrade") !== "websocket") {
    return c.text("Expected websocket", 400);
  }

  const { socket, response } = Deno.upgradeWebSocket(c.req.raw);
  
  socket.onopen = () => {
    let room = rooms.get(roomId);
    if (!room) {
      room = {
        id: roomId,
        gameState: createGameState(),
        players: []
      };
      rooms.set(roomId, room);
    }
    
    if (room.players.length >= 2) {
      socket.close(1000, "房间已满");
      return;
    }
    
    const playerId = crypto.randomUUID();
    room.players.push({ id: playerId, socket });
    room.gameState.players.push(playerId);
    
    // 发送初始状态
    socket.send(JSON.stringify({
      type: "joined",
      playerId,
      playerNumber: room.players.length,
      gameState: room.gameState
    }));
    
    // 通知其他玩家
    broadcastToRoom(room, {
      type: "playerJoined",
      gameState: room.gameState
    });
  };
  
  socket.onmessage = (event) => {
    const data = JSON.parse(event.data);
    const room = rooms.get(roomId);
    if (!room) return;
    
    if (data.type === "move") {
      const { row, col, playerId } = data;
      const playerIndex = room.gameState.players.indexOf(playerId);
      
      if (playerIndex === -1 || playerIndex + 1 !== room.gameState.currentPlayer) {
        socket.send(JSON.stringify({ type: "error", message: "不是你的回合" }));
        return;
      }
      
      if (room.gameState.board[row][col] !== 0 || room.gameState.gameOver) {
        socket.send(JSON.stringify({ type: "error", message: "无效移动" }));
        return;
      }
      
      // 执行移动
      room.gameState.board[row][col] = room.gameState.currentPlayer;
      
      // 检查胜利
      if (checkWin(room.gameState.board, row, col, room.gameState.currentPlayer)) {
        room.gameState.gameOver = true;
        room.gameState.winner = room.gameState.currentPlayer;
      } else {
        room.gameState.currentPlayer = room.gameState.currentPlayer === 1 ? 2 : 1;
      }
      
      // 广播游戏状态
      broadcastToRoom(room, {
        type: "gameUpdate",
        gameState: room.gameState
      });
    }
  };
  
  socket.onclose = () => {
    const room = rooms.get(roomId);
    if (room) {
      room.players = room.players.filter(p => p.socket !== socket);
      if (room.players.length === 0) {
        rooms.delete(roomId);
      }
    }
  };
  
  return response;
});

// API路由
app.get("/api/create-room", (c) => {
  const roomId = crypto.randomUUID();
  return c.json({ roomId });
});

// 静态文件服务
app.use("/*", serveStatic({ root: "./static" }));

Deno.serve(app.fetch);
