import { Hono } from "https://deno.land/x/hono@v3.12.11/mod.ts";
import { serveStatic } from "https://deno.land/x/hono@v3.12.11/middleware.ts";

const app = new Hono();

// 游戏状态管理
interface PhotoInfo {
  originalUrl: string;
  mosaicUrl: string;
  uploaded: boolean;
}

interface ChatMessage {
  playerId: string;
  playerNumber: number;
  message: string;
  timestamp: number;
}

interface GameState {
  board: number[][];
  currentPlayer: number;
  players: string[];
  gameOver: boolean;
  winner: number | null;
  photos: { [playerId: string]: PhotoInfo };
  chatMessages: ChatMessage[];
  gamePhase: 'waiting' | 'uploading' | 'playing' | 'finished';
}

interface Player {
  id: string;
  socket: WebSocket;
  nickname?: string;
}

interface Room {
  id: string;
  gameState: GameState;
  players: Player[];
}

const rooms = new Map<string, Room>();

// 创建新游戏状态
function createGameState(): GameState {
  return {
    board: Array(15).fill(null).map(() => Array(15).fill(0)),
    currentPlayer: 1,
    players: [],
    gameOver: false,
    winner: null,
    photos: {},
    chatMessages: [],
    gamePhase: 'waiting'
  };
}

// 检查胜利条件
function checkWin(board: number[][], row: number, col: number, player: number): boolean {
  const directions = [[1,0], [0,1], [1,1], [1,-1]];
  
  for (const [dx, dy] of directions) {
    let count = 1;
    
    // 正方向
    for (let i = 1; i < 5; i++) {
      const newRow = row + dx * i;
      const newCol = col + dy * i;
      if (newRow >= 0 && newRow < 15 && newCol >= 0 && newCol < 15 && 
          board[newRow][newCol] === player) {
        count++;
      } else break;
    }
    
    // 反方向
    for (let i = 1; i < 5; i++) {
      const newRow = row - dx * i;
      const newCol = col - dy * i;
      if (newRow >= 0 && newRow < 15 && newCol >= 0 && newCol < 15 && 
          board[newRow][newCol] === player) {
        count++;
      } else break;
    }
    
    if (count >= 5) return true;
  }
  return false;
}

// 广播消息到房间内所有玩家
function broadcastToRoom(room: Room, message: any) {
  room.players.forEach(player => {
    if (player.socket.readyState === WebSocket.OPEN) {
      player.socket.send(JSON.stringify(message));
    }
  });
}

// WebSocket 处理
app.get("/ws/:roomId", (c) => {
  const roomId = c.req.param("roomId");
  
  if (c.req.header("upgrade") !== "websocket") {
    return c.text("Expected websocket", 400);
  }

  const { socket, response } = Deno.upgradeWebSocket(c.req.raw);
  
  socket.onopen = () => {
    console.log(`🔌 新的WebSocket连接: 房间 ${roomId}`);

    let room = rooms.get(roomId);
    if (!room) {
      console.log(`🏠 创建新房间: ${roomId}`);
      room = {
        id: roomId,
        gameState: createGameState(),
        players: []
      };
      rooms.set(roomId, room);
    }

    if (room.players.length >= 2) {
      console.log(`❌ 房间已满: ${roomId}`);
      socket.close(1000, "房间已满");
      return;
    }

    const playerId = crypto.randomUUID();
    room.players.push({ id: playerId, socket });
    room.gameState.players.push(playerId);

    console.log(`👤 玩家加入: ${playerId} (房间: ${roomId}, 玩家数: ${room.players.length})`);

    // 如果有两个玩家，进入上传阶段
    if (room.players.length === 2) {
      room.gameState.gamePhase = 'uploading';
      console.log(`📸 房间 ${roomId} 进入照片上传阶段`);
    }

    // 发送初始状态
    socket.send(JSON.stringify({
      type: "joined",
      playerId,
      playerNumber: room.players.length,
      gameState: room.gameState
    }));

    // 通知其他玩家
    broadcastToRoom(room, {
      type: "playerJoined",
      gameState: room.gameState
    });
  };
  
  socket.onmessage = (event) => {
    const data = JSON.parse(event.data);
    const room = rooms.get(roomId);
    if (!room) return;
    
    if (data.type === "move") {
      const { row, col, playerId } = data;
      const playerIndex = room.gameState.players.indexOf(playerId);

      if (playerIndex === -1 || playerIndex + 1 !== room.gameState.currentPlayer) {
        socket.send(JSON.stringify({ type: "error", message: "不是你的回合" }));
        return;
      }

      if (room.gameState.board[row][col] !== 0 || room.gameState.gameOver) {
        socket.send(JSON.stringify({ type: "error", message: "无效移动" }));
        return;
      }

      // 执行移动
      room.gameState.board[row][col] = room.gameState.currentPlayer;

      // 检查胜利
      if (checkWin(room.gameState.board, row, col, room.gameState.currentPlayer)) {
        room.gameState.gameOver = true;
        room.gameState.winner = room.gameState.currentPlayer;
        room.gameState.gamePhase = 'finished';
      } else {
        room.gameState.currentPlayer = room.gameState.currentPlayer === 1 ? 2 : 1;
      }

      // 广播游戏状态
      broadcastToRoom(room, {
        type: "gameUpdate",
        gameState: room.gameState
      });
    } else if (data.type === "chat") {
      const { message, playerId } = data;
      const playerIndex = room.gameState.players.indexOf(playerId);

      if (playerIndex === -1) {
        socket.send(JSON.stringify({ type: "error", message: "无效的玩家" }));
        return;
      }

      const chatMessage: ChatMessage = {
        playerId,
        playerNumber: playerIndex + 1,
        message,
        timestamp: Date.now()
      };

      room.gameState.chatMessages.push(chatMessage);

      // 广播聊天消息
      broadcastToRoom(room, {
        type: "chatMessage",
        message: chatMessage
      });
    } else if (data.type === "setNickname") {
      const { nickname, playerId } = data;
      const player = room.players.find(p => p.id === playerId);

      if (player) {
        player.nickname = nickname;

        // 广播昵称更新
        broadcastToRoom(room, {
          type: "nicknameUpdate",
          playerId,
          nickname
        });
      }
    }
  };
  
  socket.onclose = () => {
    const room = rooms.get(roomId);
    if (room) {
      room.players = room.players.filter(p => p.socket !== socket);
      if (room.players.length === 0) {
        rooms.delete(roomId);
      }
    }
  };
  
  return response;
});

// 简化：不再生成马赛克图片，前端使用CSS模糊效果

// API路由
app.get("/api/create-room", (c) => {
  const roomId = crypto.randomUUID();
  return c.json({ roomId });
});

// 照片上传API
app.post("/api/upload-photo/:roomId", async (c) => {
  const roomId = c.req.param("roomId");
  const formData = await c.req.formData();
  const file = formData.get("photo") as File;
  const playerId = formData.get("playerId") as string;

  if (!file || !playerId) {
    return c.json({ error: "缺少文件或玩家ID" }, 400);
  }

  const room = rooms.get(roomId);
  if (!room) {
    return c.json({ error: "房间不存在" }, 404);
  }

  try {
    // 保存原图
    const fileExtension = file.name.split('.').pop() || 'jpg';
    const fileName = `${playerId}_${Date.now()}.${fileExtension}`;
    const filePath = `./uploads/${fileName}`;

    const arrayBuffer = await file.arrayBuffer();
    await Deno.writeFile(filePath, new Uint8Array(arrayBuffer));

    console.log(`📸 照片上传成功: ${fileName} (玩家: ${playerId})`);

    // 更新游戏状态 - 简化：只存储原图URL，前端处理模糊效果
    room.gameState.photos[playerId] = {
      originalUrl: `/uploads/${fileName}`,
      mosaicUrl: `/uploads/${fileName}`, // 同样的URL，前端决定是否模糊
      uploaded: true
    };

    // 检查是否两个玩家都上传了照片
    const uploadedCount = Object.keys(room.gameState.photos).length;
    if (uploadedCount === 2) {
      room.gameState.gamePhase = 'playing';
    }

    // 广播更新
    broadcastToRoom(room, {
      type: "photoUploaded",
      gameState: room.gameState
    });

    return c.json({ success: true });
  } catch (error) {
    console.error('上传照片失败:', error);
    return c.json({ error: "上传失败" }, 500);
  }
});

// 静态文件服务
app.use("/uploads/*", serveStatic({ root: "./uploads" }));
app.use("/*", serveStatic({ root: "./static" }));

console.log("正在启动服务器...");
try {
  await Deno.serve({
    port: 8000,
    onListen: ({ port, hostname }) => {
      console.log(`🚀 服务器已启动: http://${hostname}:${port}`);
      console.log("📁 静态文件目录: ./static");
      console.log("📸 上传目录: ./uploads");
    }
  }, app.fetch);
} catch (error) {
  console.error("❌ 服务器启动失败:", error);
}


